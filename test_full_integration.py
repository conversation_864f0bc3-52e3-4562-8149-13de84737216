#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试融合后的百度相关搜索词功能
"""

import sys
import os
import time

# 动态导入包含特殊字符的模块
import importlib.util
spec = importlib.util.spec_from_file_location("bd_wap_helper", "bd_wap - 采集助手.py")
bd_wap_helper = importlib.util.module_from_spec(spec)
spec.loader.exec_module(bd_wap_helper)

def test_full_mining():
    """测试完整的挖掘功能"""
    print("开始测试完整的关键词挖掘功能...")
    
    # 使用命令行模式运行
    original_argv = sys.argv
    try:
        # 模拟命令行参数
        sys.argv = ['bd_wap - 采集助手.py', '--cli']
        
        # 修改配置
        bd_wap_helper.CONFIG = {
            'thread_num': 2,       # 减少线程数以便测试
            'max_depth': 2,        # 减少深度以便快速测试
            'seed_file': 'test_keywords.txt',
            'output_file': 'test_results.txt',
            'failed_file': 'test_failed.log',
            'use_proxy': False     # 不使用代理以便测试
        }
        
        print("配置参数:")
        for key, value in bd_wap_helper.CONFIG.items():
            print(f"  {key}: {value}")
        
        # 运行主函数
        bd_wap_helper.main()
        
        # 检查结果
        if os.path.exists('test_results.txt'):
            with open('test_results.txt', 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"\n结果文件内容:\n{content}")
        else:
            print("结果文件未生成")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    finally:
        sys.argv = original_argv

if __name__ == "__main__":
    test_full_mining()
