#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试融合后的百度相关搜索词功能
"""

import sys
import os

# 导入主模块
import importlib.util
import sys
from queue import Queue
import threading

# 动态导入包含特殊字符的模块
spec = importlib.util.spec_from_file_location("bd_wap_helper", "bd_wap - 采集助手.py")
bd_wap_helper = importlib.util.module_from_spec(spec)
spec.loader.exec_module(bd_wap_helper)

BaiduMobileSpider = bd_wap_helper.BaiduMobileSpider

def test_html_related_search():
    """测试HTML相关搜索功能"""
    print("测试百度HTML相关搜索功能...")
    
    # 创建一个简单的测试实例
    task_queue = Queue()
    failed_log = open('test_failed.log', 'w', encoding='utf-8')
    
    try:
        spider = BaiduMobileSpider(task_queue, 1, failed_log)
        
        # 测试关键词
        test_keyword = "机器学习"
        print(f"测试关键词: {test_keyword}")
        
        # 调用新的HTML相关搜索方法
        related_words = spider.get_baidu_html_related_searches(test_keyword)
        
        if related_words:
            print(f"成功获取到 {len(related_words)} 个相关搜索词:")
            for i, word in enumerate(related_words, 1):
                print(f"  {i}. {word}")
        else:
            print("未获取到相关搜索词")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    finally:
        failed_log.close()

if __name__ == "__main__":
    test_html_related_search()
