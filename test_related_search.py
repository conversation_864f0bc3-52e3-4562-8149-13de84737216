#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from bs4 import BeautifulSoup
import re

def get_baidu_related_searches(keyword, count=10):
    """
    获取百度相关搜索词 (模拟百度智能推荐引擎)
    :param keyword: 要查询的关键词
    :param count: 返回的相关词数量
    :return: 相关搜索词列表
    """
    url = "https://www.baidu.com/s"
    params = {
        'wd': keyword,
        'rn': count,  # 返回结果数量
        'ie': 'utf-8'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)

        if response.status_code == 200:
            # 从HTML中解析相关搜索词
            soup = BeautifulSoup(response.text, 'html.parser')
            related_words = []
            
            # 右侧相关搜索
            related_div_right = soup.find('div', {'card-type': 'rg'})
            if related_div_right and related_div_right.get('card-show-log'):
                try:
                    card_show_log = related_div_right['card-show-log']
                    card_show_log_data = json.loads(card_show_log)
                    if 'iteminfo' in card_show_log_data:
                        related_items = card_show_log_data['iteminfo']
                        right_related_words = [item['item'] for item in related_items if 'item' in item]
                        related_words.extend(right_related_words)
                        print(f"右侧相关搜索: {right_related_words}")
                except (json.JSONDecodeError, KeyError) as e:
                    print(f"解析右侧相关搜索失败: {e}")
            
            # 查找底部相关搜索区域
            related_div = soup.find('div', {'id': 'rs_new'})
            if related_div:
                related_links = related_div.find_all('a')
                bottom_related_words = [link.text.strip() for link in related_links if link.text.strip()]
                related_words.extend(bottom_related_words)
                print(f"底部相关搜索: {bottom_related_words}")
            
            # 清理和去重
            cleaned_words = []
            for word in related_words:
                cleaned = word.strip()
                if cleaned and len(cleaned) >= 2 and len(cleaned) <= 50:
                    if not re.match(r'^[\d\s\-_=+<>()（）【】\[\]]+$', cleaned):
                        cleaned_words.append(cleaned)
            
            return list(set(cleaned_words))  # 去重
            
    except Exception as e:
        print(f"获取百度相关搜索出错: {e}")
    
    return []

# 测试功能
if __name__ == "__main__":
    keywords = ["种植牙", "补牙", "牙齿矫正"]
    
    for keyword in keywords:
        print(f"\n=== 测试关键词: {keyword} ===")
        related_searches = get_baidu_related_searches(keyword, 8)
        print(f"与 '{keyword}' 相关的搜索词:")
        for i, word in enumerate(related_searches, 1):
            print(f"{i}. {word}")
        print(f"总共获得 {len(related_searches)} 个相关词")
