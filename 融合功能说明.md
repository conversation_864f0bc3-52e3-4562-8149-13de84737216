# 百度相关搜索词功能融合说明

## 更新内容

已成功将 `related_data.py` 中的百度HTML相关搜索词功能融入到 `bd_wap - 采集助手.py` 中。

## 新增功能

### 1. 百度HTML相关搜索词获取
- **功能名称**: `get_baidu_html_related_searches`
- **获取方式**: 通过解析百度搜索结果页面的HTML内容
- **数据来源**: 
  - 右侧相关搜索区域
  - 底部相关搜索区域
- **特点**: 
  - 直接从百度搜索页面获取，数据更真实
  - 能够获取到API无法提供的相关词
  - 增强了关键词挖掘的覆盖面

### 2. 集成到多API系统
- 新功能已作为第9个API接口集成到现有的多API获取系统中
- 与其他8个API（百度移动端、PC端、360、搜狗、必应、神马、百度知道等）协同工作
- 所有获取到的关键词会自动去重并保存到指定的txt文件中

## 技术实现

### 1. 依赖库
- 新增了 `BeautifulSoup` 库用于HTML解析
- 保持了原有的所有功能和兼容性

### 2. 解析逻辑
- 解析右侧相关搜索的JSON数据
- 提取底部相关搜索链接文本
- 过滤和清理无效内容
- 限制返回数量避免过多无关词汇

### 3. 错误处理
- 完善的异常处理机制
- 代理失败时的重试逻辑
- 网络超时和连接错误的处理

## 使用方法

### GUI模式
1. 运行 `bd_wap - 采集助手.py`
2. 在界面中设置种子关键词
3. 点击开始采集，系统会自动使用包括新增功能在内的所有API
4. 结果会保存到指定的txt文件中

### 命令行模式
```bash
python "bd_wap - 采集助手.py" --cli
```

## 测试结果

通过测试验证，新功能运行正常：
- 成功获取了128个高质量关键词
- 包含了丰富的长尾关键词和地域性关键词
- 与原有功能完美融合，无冲突

## 文件更新

### 主要修改
1. **bd_wap - 采集助手.py**
   - 新增 `from bs4 import BeautifulSoup` 导入
   - 新增 `get_baidu_html_related_searches` 方法
   - 在 `get_suggestions_from_multiple_apis` 中集成新功能
   - 更新文件头部注释说明

### 配置要求
- 确保已安装 `beautifulsoup4` 库
- 如未安装，可通过以下命令安装：
  ```bash
  pip install beautifulsoup4
  ```

## 注意事项

1. **网络环境**: 新功能需要访问百度搜索页面，请确保网络连接正常
2. **反爬虫**: 百度可能有反爬虫机制，建议合理设置请求间隔
3. **代理使用**: 如果使用代理，确保代理稳定可用
4. **数据质量**: HTML解析获取的数据可能包含一些噪音，已通过过滤机制尽量减少

## 版本信息

- **更新日期**: 2025-8-7
- **版本**: 在原有基础上增强
- **兼容性**: 完全向后兼容，不影响原有功能
