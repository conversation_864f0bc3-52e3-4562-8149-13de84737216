#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from datetime import datetime

# 添加当前目录到路径
sys.path.append('.')

# 导入主模块
from main import BaiduMobileSpider
from queue import Queue
import threading

def test_full_flow():
    """测试完整的挖掘流程"""
    
    # 清空之前的结果
    BaiduMobileSpider.result.clear()
    BaiduMobileSpider.related_result.clear()
    BaiduMobileSpider.seen.clear()
    BaiduMobileSpider.stop_flag = False
    
    # 创建任务队列
    task_queue = Queue()
    
    # 添加测试关键词
    test_keywords = ["种植牙", "补牙"]
    for keyword in test_keywords:
        task_queue.put((keyword, 1))
    
    # 创建失败日志文件
    failed_log = open('test_failed.log', 'w', encoding='utf-8')
    
    # 创建爬虫实例
    spider = BaiduMobileSpider(task_queue, 2, failed_log)  # 最大深度设为2
    
    print("开始测试挖掘流程...")
    
    # 手动处理几个关键词
    for keyword in test_keywords:
        print(f"\n处理关键词: {keyword}")
        spider.process_keyword(keyword, 1)
        
        print(f"当前下拉词数量: {len(BaiduMobileSpider.result)}")
        print(f"当前相关词数量: {len(BaiduMobileSpider.related_result)}")
    
    # 显示结果
    print(f"\n=== 最终结果 ===")
    print(f"下拉词总数: {len(BaiduMobileSpider.result)}")
    print(f"相关词总数: {len(BaiduMobileSpider.related_result)}")
    
    if BaiduMobileSpider.result:
        print(f"\n下拉词示例 (前10个):")
        for i, word in enumerate(list(BaiduMobileSpider.result)[:10], 1):
            print(f"  {i}. {word}")
    
    if BaiduMobileSpider.related_result:
        print(f"\n相关词示例 (前10个):")
        for i, word in enumerate(list(BaiduMobileSpider.related_result)[:10], 1):
            print(f"  {i}. {word}")
    
    # 保存结果
    save_results()
    
    failed_log.close()

def save_results():
    """保存结果到文件"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 保存下拉词结果
    if BaiduMobileSpider.result:
        with open('test_dropdown_results.txt', 'w', encoding='utf-8') as f:
            f.write(f"# 百度移动端下拉词挖掘结果\n")
            f.write(f"# 生成时间: {timestamp}\n")
            f.write(f"# 下拉词数量: {len(BaiduMobileSpider.result)}\n\n")
            for keyword in sorted(BaiduMobileSpider.result):
                f.write(f"{keyword}\n")
        print(f"\n下拉词已保存到: test_dropdown_results.txt")

    # 保存相关搜索词结果
    if BaiduMobileSpider.related_result:
        with open('test_related_results.txt', 'w', encoding='utf-8') as f:
            f.write(f"# 百度相关搜索词挖掘结果\n")
            f.write(f"# 生成时间: {timestamp}\n")
            f.write(f"# 相关词数量: {len(BaiduMobileSpider.related_result)}\n\n")
            for keyword in sorted(BaiduMobileSpider.related_result):
                f.write(f"{keyword}\n")
        print(f"相关词已保存到: test_related_results.txt")

if __name__ == "__main__":
    test_full_flow()
